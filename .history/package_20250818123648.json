{"name": "@nextssp/web", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --verbose", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint --fix .", "test:typecheck": "TYPECHECK=true nuxt prepare && TYPECHECK=true nuxt typecheck"}, "dependencies": {"@iconify-json/lucide": "^1.2.62", "@iconify-json/simple-icons": "^1.2.47", "@nuxt/content": "^3.6.3", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^2.0.0", "@nuxt/image": "^1.11.0", "@nuxt/scripts": "^0.11.10", "@nuxt/test-utils": "^3.19.2", "@nuxt/ui": "^3.3.2", "@unhead/vue": "^2.0.14", "@tanstack/vue-query": "^5.85.3", "@tanstack/vue-table": "^8.21.3", "nuxt": "^4.0.3", "pinia": "^3.0.3"}, "devDependencies": {"@eslint/js": "^9.33.0", "@nuxt/eslint": "^1.8.0", "@stylistic/eslint-plugin": "^5.2.3", "eslint": "^9.33.0", "prettier": "^3.6.2", "prettier-plugin-jsdoc": "^1.3.3", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1", "unplugin-vue-components": "^29.0.0", "vite-plugin-eslint2": "^5.0.4", "vite-plugin-oxlint": "^1.4.0", "vite-plugin-stylelint": "^6.0.2"}, "lint-staged": {"*.{js,mjs,ts,tsx,mts,cts,vue,md,json}": ["eslint --cache --fix"]}}