<script setup lang="ts">
import { useMainStore } from '~/stores';

const props = withDefaults(defineProps<{
  open?: boolean;
}>(), { open: false });
const emit = defineEmits(['update:open']);

const mainStore = useMainStore();
const { leftBarOpen } = storeToRefs(mainStore);

const openLeftBar = () => {
  mainStore.leftBarOpen = true;
};

const closeLeftBar = () => {
  mainStore.leftBarOpen = false;
};

watchEffect(() => {
  mainStore.leftBarOpen = props.open;
});
</script>

<template>
  <UDrawer
    :open="leftBarOpen"
    direction="left"
    title="LeftBar title"
    description="LeftBar description..."
    min
    @update:open="emit('update:open', $event)"
    @close="closeLeftBar"
  >
    <UButton
      label="Open"
      color="neutral"
      variant="subtle"
      trailing-icon="i-lucide-chevron-right"
      class="absolute top-1/2 left-0"
      @click="openLeftBar"
    />

    <template #body>
      <Placeholder class="min-w-96 min-h-96 size-full m-4" />
    </template>
  </UDrawer>
</template>
