<script setup lang="ts">
import { useMainStore } from '~/stores';

const props = defineProps<{
  open: boolean;
}>();
const emit = defineEmits(['update:open']);

const mainStore = useMainStore();
const { leftBarOpen } = storeToRefs(mainStore);

const closeLeftBar = () => {
  mainStore.leftBarOpen = false;
};

watchEffect(() => {
  if (props.open) {
    mainStore.leftBarOpen = true;
  }
});
</script>

<template>
  <UDrawer
    :open="leftBarOpen"
    direction="left"
    title="Drawer with title"
    description="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    @update:open="emit('update:open', $event)"
    @close="closeLeftBar"
  >
    <template #body>
      <Placeholder class="min-w-96 min-h-96 size-full m-4" />
    </template>
  </UDrawer>
</template>
