<script setup lang="ts">
import { useMainStore } from '~/stores';

const props = defineProps<{
  open: boolean;
}>();
const emit = defineEmits(['update:open']);

const { leftBarOpen } = useMainStore();
</script>

<template>
  <UDrawer
    :open="props.open || leftBarOpen"
    direction="left"
    title="Drawer with title"
    description="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    @update:open="emit('update:open', $event)"
    @close="leftBarOpen = false"
  >
    <template #body>
      <Placeholder class="min-w-96 min-h-96 size-full m-4" />
    </template>
  </UDrawer>
</template>
