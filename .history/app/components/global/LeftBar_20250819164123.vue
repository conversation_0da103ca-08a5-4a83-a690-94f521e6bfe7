<script setup lang="ts">
const props = defineProps<{
  open: boolean;
}>();
const emit = defineEmits(['update:open']);
</script>

<template>
  <UDrawer
    :open="props.open"
    direction="left"
    title="Drawer with title"
    description="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    @update:open="emit('update:open', $event)"
  >
    <template #content>
      <Placeholder class="min-w-96 min-h-96 size-full m-4" />
    </template>
  </UDrawer>
</template>
