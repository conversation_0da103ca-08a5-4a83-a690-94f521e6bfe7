import { defineNuxtConfig } from 'nuxt/config';

export default defineNuxtPlugin(nuxtApp => {
  // called right before setting a new locale
  nuxtApp.hook('i18n:beforeLocaleSwitch', (switch) => {
    console.log('onBeforeLanguageSwitch', switch.oldLocale, switch.newLocale, switch.initialSetup)

    // You can override the new locale by setting it to a different value
    if(switch.newLocale === 'fr') {
      switch.newLocale = 'en'
    }
  })

  // called right after a new locale has been set
  nuxtApp.hook('i18n:localeSwitched', (switch) => {
    console.log('onLanguageSwitched', switch.oldLocale, switch.newLocale)
  })
});
