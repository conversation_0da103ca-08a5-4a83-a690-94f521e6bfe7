<script setup lang="ts">
import { useMainStore } from '~/stores';

const mainStore = useMainStore();
// const { leftBarOpen } = mainStore;

const openLeftBar = () => {
  mainStore.leftBarOpen = true;
};
</script>

<template>
  <div>
    <TopBar />
    <LeftBar />
    <UButton
      label="Open"
      color="neutral"
      variant="subtle"
      trailing-icon="i-lucide-chevron-right"
      class="absolute top-1/2 left-0"
      @click="openLeftBar"
    />
    <main>
      <AppHeader />
      <slot />
      <AppFooter />
    </main>
  </div>
</template>
