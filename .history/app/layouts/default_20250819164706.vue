<script setup lang="ts">
const drawerOpen = useMainStore(false);
</script>

<template>
  <div>
    <TopBar />
    <LeftBar :open="drawerOpen" />
    <UButton
      label="Open"
      color="neutral"
      variant="subtle"
      trailing-icon="i-lucide-chevron-up"
      class="absolute top-1/2 left-0"
      @click="drawerOpen = !drawerOpen"
    />
    <main>
      <AppHeader />
      <slot />
      <AppFooter />
    </main>
  </div>
</template>
