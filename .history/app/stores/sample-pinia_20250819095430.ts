//

interface SampleStore { name: string; description: string }

export const useSampleStore = defineStore('websiteStore', {
  state: (): { name: string; description: string } => ({
    name: '',
    description: '',
  }),
  actions: {
    async fetch() {
      const infos = await $fetch('https://api.nuxt.com/modules/pinia');

      this.name = infos.name;
      this.description = infos.description;
    },
  },
});
