interface MainState {
  name: string;
  description: string;
  leftBarOpen: boolean;
}

export const useMainStore = defineStore('main', {
  state: (): MainState => ({
    name: '',
    description: '',
    leftBarOpen: false,
  }),
  actions: {
    async fetch() {
      const infos: MainState = await $fetch('https://api.nuxt.com/modules/pinia');

      this.name = infos.name;
      this.description = infos.description;
    },
    toggleLeftBar() {
      this.leftBarOpen = !this.leftBarOpen;
    },
  },
  getters: {
    isLeftBarOpen: (state) => state.leftBarOpen,
  },
});
