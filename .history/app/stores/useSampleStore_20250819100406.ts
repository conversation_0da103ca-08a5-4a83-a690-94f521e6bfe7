//

interface SampleState { name: string; description: string }

export const useSampleStore = defineStore('sampleStore', {
  state: (): SampleState => ({
    name: '',
    description: '',
  }),
  actions: {
    async fetch() {
      const infos: SampleState = await $fetch('https://api.nuxt.com/modules/pinia');

      this.name = infos.name;
      this.description = infos.description;
    },
  },
});
