<script setup lang="ts">
useSeoMeta({
  title: 'Next Samsung+',
  ogTitle: 'Next Samsung+',
  description: 'This is the Next Samsung+ site, let me tell you all about it.',
  ogDescription: 'This is the Next Samsung+ site, let me tell you all about it.',
  ogImage: 'https://samsungplus.com/image.png',
  twitterCard: 'summary_large_image',
});

// You might choose this based on an API call or logged-in status
const layout = 'default';

onMounted(async () => {
  console.log('🎯 App mounted!');

  /*
  // NOTE: 동적으로 css를 import하여 적용하는 방법
  if (import.meta.client) {
    if (this.$device.isMobile)
      await import('~/assets/scss/mobile/index.scss');
    else
      await import('~/assets/scss/pc/index.scss');
  } */
});
</script>

<template>
  <UApp>
    <NuxtLayout :name="layout">
      <NuxtPage />
    </NuxtLayout>
  </UApp>
</template>
