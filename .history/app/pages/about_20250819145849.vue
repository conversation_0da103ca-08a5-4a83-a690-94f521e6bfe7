<script setup lang="ts">
definePageMeta({
  layout: 'mobile-default',
});

const { t: $t } = useI18n();
// const localePath = useLocalePath();
</script>

<template>
  <div class="page flex flex-col items-center justify-center gap-4 h-screen">
    <h1 class="font-bold text-2xl text-(--ui-primary)">
      About <ColorModeButton />
    </h1>

    <div class="flex items-center gap-2 bg-accented">
      <NuxtLinkLocale to="/">{{ $t('home') }}</NuxtLinkLocale>
    </div>
  </div>
</template>

<style>
@media (min-width: 768px) {
  .page {
    max-width: 768px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
  }
}
</style>
