export const useLocale = () => {
  return useState<string>('locale', () => useDefaultLocale().value);
};

export const useDefaultLocale = (fallback = 'en-US') => {
  const locale = ref(fallback);
  if (import.meta.server) {
    const reqLocale = useRequestHeaders()['accept-language']?.split(',')[0];
    if (reqLocale) {
      locale.value = reqLocale;
    }
  }
  else if (import.meta.client) {
    const navLang = navigator.language;
    if (navLang) {
      locale.value = navLang;
    }
  }
  return locale;
};

export const useLocales = () => {
  const locale = useLocale();
  const locales = ref([
    'ko-KR',
    'en-US',
    'en-GB',
  ]);
  if (!locales.value.includes(locale.value)) {
    locales.value.unshift(locale.value);
  }
  return locales;
};

export const useLocaleDate = (date: Ref<Date> | Date, locale = useLocale()) => {
  return computed(() => new Intl.DateTimeFormat(locale.value, { dateStyle: 'full' }).format(unref(date)));
};
