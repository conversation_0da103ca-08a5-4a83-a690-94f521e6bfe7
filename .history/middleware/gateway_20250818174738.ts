import { defineNuxtRouteMiddleware, abortNavigation, navigateTo } from 'nuxt/app';
import { RouteLocationNormalized, NavigationGuard, RouteLocationRaw } from 'vue-router';
// import { useAuthStore } from '@/stores/auth';

export default defineNuxtRouteMiddleware(
    (to: RouteLocationNormalized, from: RouteLocationNormalized): ReturnType<NavigationGuard> => {
  if (to.params.id === '1') {
    return abortNavigation()
  }
  // In a real app you would probably not redirect every route to `/`
  // however it is important to check `to.path` before redirecting or you
  // might get an infinite redirect loop
  if (to.path !== '/') {
    return navigateTo('/')
  }
});
