{
  "compilerOptions": {
    "forceConsistentCasingInFileNames": true,
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": true,
    "importHelpers": true,
    "isolatedModules": true,
    "lib": ["es2022"],
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "noEmitOnError": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "es2022",
    "customConditions": ["development"],
    "baseUrl": ".",
    "paths": {
      // "@nextssp/ui-primevue": ["libs/ui-primevue/src/index.ts"]
      images: './assets/images',
      style: './assets/style',
      data: './assets/other/data',
      utils: './utils',
    }
  }
}
