import path from 'node:path';
import { defineNuxtConfig } from 'nuxt/config'
// import { defineContentConfig, defineCollection } from '@nuxt/content';

import oxlint from 'vite-plugin-oxlint';
import eslint from 'vite-plugin-eslint2';
import stylelint from 'vite-plugin-stylelint';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },

  debug: true,

  sourcemap: true,
  /* sourcemap: {
    'server': true,
    'client': false
  }, */

  $development: {
    //
  },

  $env: {
    staging: {
      //
    },
  },

  $production: {
    routeRules: {
      '/**': { isr: true },
    },
  },

  devServer: {
    host: 'localhost',
    port: 4200,
  },

  ssr: true,

  // when enabling ssr options, you need to disable inlineStyles and maybe devLogs
  features: {
    devLogs: false,
    inlineStyles: false,
  },

  experimental: {
    debugModuleMutation: false,
  },

  nitro: {
    minify: false,
    inlineDynamicImports: true
  },

  app: {
    head: {
      /* NOTE: 외부 static css 등을 추가하고 싶을 때,
      link: [{ rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css' }]
      */
    },
  },

  alias: {
    '~': path.resolve(__dirname, './app'),
  },

  modules: [
    '@nuxt/ui',
    '@nuxt/eslint',
    '@nuxt/content',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/test-utils',
    '@nuxt/scripts',
    '@nuxtjs/i18n',
  ],

  css: ['~/assets/css/main.css'],

  compatibilityDate: '2025-07-16',

  future: {
    compatibilityVersion: 4,
  },

  components: true,

  vite: {
    plugins: [
      oxlint(),
      eslint(),
      stylelint(),
    ],
    css: {
      devSourcemap: true,
      /* preprocessorOptions: {
        scss: {
        },
      }, */
    },
    vue: {
      customElement: true,
    },
    vueJsx: {
      mergeProps: true,
    },
  },

  eslint: {
    config: {
      stylistic: {
        quotes: 'single', // Enforce single quotes
        // You can also add other stylistic rules here, e.g.,
        semi: true,
        indent: 2,
      },
    },
  },

  ui: {
    // prefix: 'U',
    // colorMode: false,
    // fonts: false,
    theme: {
      colors: [
        'primary',
        'secondary',
        'tertiary',
        'info',
        'success',
        'warning',
        'error',
      ],
    },
  },

  i18n: {
    locales: [
      { code: 'en', language: 'en-US' },
      { code: 'fr', language: 'fr-FR' }
    ],
    defaultLocale: 'en',
    experimental: {
      preload: true,
    },
  },
})
