import path from 'node:path';
import { defineNuxtConfig } from 'nuxt/config'
// import { defineContentConfig, defineCollection } from '@nuxt/content';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },

  debug: true,

  $development: {
    //
  },

  $env: {
    staging: {
      //
    },
  },

  $production: {
    routeRules: {
      '/**': { isr: true },
    },
  },

  devServer: {
    host: 'localhost',
    port: 4200,
  },
  
  ssr: true,

  experimental: {
    debugModuleMutation: false,
    inlineSSRStyles: true,
  },

  nitro: {
    inlineDynamicImports: true
  },

  app: {
    head: {
      /* NOTE: 외부 static css 등을 추가하고 싶을 때,
      link: [{ rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css' }]
      */
    },
  },

  css: ['~/assets/css/main.css'],

  alias: {
    '~': path.resolve(__dirname, './app'),
  },

  modules: [
    '@nuxt/ui',
    '@nuxt/eslint',
    '@nuxt/content',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/test-utils',
    '@nuxt/scripts'
  ],

  css: ['~/assets/css/main.css'],

  compatibilityDate: '2025-07-16',

  components: true,

  vite: {
    plugins: [
      // ...linters,
      // tailwindcss(),
    ],
    css: {
      devSourcemap: true,
    },
    vue: {
      customElement: true,
    },
    vueJsx: {
      mergeProps: true,
    },
  },

  eslint: {
    config: {
      stylistic: {
        quotes: 'single', // Enforce single quotes
        // You can also add other stylistic rules here, e.g.,
        semi: true,
        indent: 2,
      },
    },
  },

  ui: {
    // prefix: 'U',
    theme: {
      colors: [
        'primary',
        'secondary',
        'tertiary',
        'info',
        'success',
        'warning',
        'error',
      ],
    },
  },
})