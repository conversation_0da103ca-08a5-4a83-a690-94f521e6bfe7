import type { Config } from 'prettier';
import type { PluginOptions } from 'prettier-plugin-tailwindcss';

/**
 * @see https://prettier.io/docs/configuration
 * @type {import("prettier").Config & import('prettier-plugin-tailwindcss').PluginOptions}}
 */
const config: Config & PluginOptions = {
  singleQuote: true,
  semi: true,
  trailingComma: 'all',
  printWidth: 120,
  tabWidth: 2,
  useTabs: false,
  endOfLine: 'lf',
  bracketSpacing: true,
  arrowParens: 'always',
  plugins: [
    'prettier-plugin-jsdoc',
    // WARN: This plugin must be loaded last to avoid conflicts with other plugins
    'prettier-plugin-tailwindcss',
  ],
};

export default config;
