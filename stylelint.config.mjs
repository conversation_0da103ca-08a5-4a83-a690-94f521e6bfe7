// import { type Config } from 'stylelint';

/**
 * @see https://stylelint.io/user-guide/configure/
 * @type {import("stylelint").Config}
 */
const config = {
  extends: 'stylelint-config-recommended',
  rules: {
    /* 'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: ['extends'],
      },
    ],
    'block-no-empty': null,
    'unit-allowed-list': ['em', 'rem', 's'], */
  },
};

export default config;
