# Next Samsung+ Web

Look at [Nuxt docs](https://nuxt.com/docs/getting-started/introduction) and [Nuxt UI docs](https://ui.nuxt.com) to learn more.

## Setup

Make sure to install the dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:4200`:

```bash
# npm
npm run dev

# pnpm
pnpm run dev

# yarn
yarn dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm run build

# yarn
yarn build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm run preview

# yarn
yarn preview
```

Lint:

```bash
# npm
npm lint

# pnpm
pnpm lint
```

Lint with fix:

```bash
# npm
npm lint:fix

# pnpm
pnpm lint:fix
```

Test typecheck:

```bash
# npm
npm test:typecheck

# pnpm
pnpm test:typecheck
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## Naming Rules

### Component

PascalCase

- "<PascalCase>Component</PascalCase>": "components/PascalCase.vue"
- "<PascalCase>Page</PascalCase>": "page/PascalCase.vue"

### Composable

camelCase + use prefix

- "const { ... } = useCamelCase()": "composables/useCamelCase.ts"
- "const { ... } = useCamelCase()": "stores/useCamelCase.ts"
- export pattern

  ```ts
  export const useCamelCase = () => {
    ...
  }
  ```

-
