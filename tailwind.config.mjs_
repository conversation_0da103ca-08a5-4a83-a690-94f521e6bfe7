// import type { Config } from 'tailwindcss';

/**
 * @see https://stylelint.io/user-guide/configure/
 * @type {import("tailwindcss").Config}
 */
const config = {
  content: ['./app/**/*.{html,js,jsx,mjs,md,mdx,ts,tsx,mts,vue}'],
  purge: [],
  darkMode: false, // or 'media' or 'class'
  theme: {
    extend: {},
  },
  variants: {
    extend: {},
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    require('tailwindcss-animate'),
    // require('@headlessui/tailwindcss')({ prefix: 'ui' }),
  ],
};

export default config;
