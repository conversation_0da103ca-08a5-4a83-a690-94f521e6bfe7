// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs';
import jseslint from '@eslint/js';
import tseslint from 'typescript-eslint';
import stylistic from '@stylistic/eslint-plugin';

export default withNuxt(
  {
    ignores: [
      '**/.*',
      // '**/*.config.*',
      '**/._*', '**/dist',
      '.nuxt/**',
      '.output/**',
      'node_modules',
      'modules/**',
      'plugins/**',
    ],
    // extends: ['@nuxt/eslint-config', 'prettier'],
  },
  jseslint.configs.recommended,
  tseslint.configs.eslintRecommended,
  // ...tseslint.configs.recommended,
  stylistic.configs.recommended,
  {
    files: ['**/*.vue'],
    languageOptions: {
      parserOptions: {
        parser: await import('@typescript-eslint/parser'),
      },
    },
  },
  {
    files: [
      '**/*.ts',
      '**/*.tsx',
      '**/*.cts',
      '**/*.mts',
      '**/*.js',
      '**/*.jsx',
      '**/*.cjs',
      '**/*.mjs',
    ],
    // Override or add rules here
    rules: {},
  },
  {
    rules: {
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': ['warn',
        {
          vars: 'all',
          varsIgnorePattern: '^_',
          args: 'after-used',
          argsIgnorePattern: '^_',
        },
      ],
      'import/newline-after-import': [
        'error',
        {
          // "count": 1,
          exactCount: true,
          considerComments: true,
        },
      ],
      // Stylistic
      'max-len': 'off',
      '@stylistic/max-len': ['warn', { code: 120, tabWidth: 2, ignoreComments: true }],
      // semi: ['error', 'always'],
      'sort-keys': 'off',
      semi: 'off',
      '@stylistic/semi': ['error', 'always'],
      quotes: 'off',
      '@stylistic/quote-props': ['error', 'as-needed'],
      '@stylistic/arrow-parens': ['error', 'always'],
      'member-delimiter-style': 'off',
      '@stylistic/member-delimiter-style': ['error', {
        multiline: {
          delimiter: 'comma',
          requireLast: true,
        },
        singleline: {
          delimiter: 'comma',
          requireLast: false,
        },
        overrides: {
          interface: {
            multiline: {
              delimiter: 'semi',
              requireLast: true,
            },
            singleline: {
              delimiter: 'semi',
              requireLast: false,
            },
          },
          typeLiteral: {
            multiline: {
              delimiter: 'semi',
              requireLast: true,
            },
            singleline: {
              delimiter: 'semi',
              requireLast: false,
            },
          },
        },
      },
      ],
      // Typescript
      '@typescript-eslint/no-empty-object-type': [
        'warn',
        {
          allowInterfaces: 'with-single-extends',
          allowObjectTypes: 'never',
          allowWithName: 'Props$',
        },
      ],
      '@typescript-eslint/no-unsafe-function-type': ['warn'],
      '@typescript-eslint/no-wrapper-object-types': ['warn'],
      // '@typescript-eslint/no-explicit-any': ['warn'],
      '@typescript-eslint/no-explicit-any': [
        'warn', // error 대신 warn 사용
        {
          ignoreRestArgs: true,
          fixToUnknown: false,
        },
      ],
      // Vue
      'vue/multi-word-component-names': [
        'warn',
        {
          ignores: ['App', 'Index'],
        },
      ],
      'vue/singleline-html-element-content-newline': 'off',
      /* 'vue/singleline-html-element-content-newline': [
        'error',
        {
          ignoreWhenNoAttributes: true,
          ignoreWhenEmpty: true,
          ignores: ['pre', 'textarea', 'span', 'button', 'label', 'a', 'code', 'small'],
        },
      ], */
    },
  },
  {
    files: ['**/*.config.*'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },
);

