// import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { defineNuxtConfig } from 'nuxt/config';

// import { defineContentConfig, defineCollection } from '@nuxt/content';

// import { federation } from '@module-federation/vite';
// import topLevelAwait from 'vite-plugin-top-level-await';

import oxlint from 'vite-plugin-oxlint';
import eslint from 'vite-plugin-eslint2';
import stylelint from 'vite-plugin-stylelint';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({

  modules: [
    '@nuxt/ui',
    '@nuxt/eslint',
    '@nuxt/content',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/test-utils',
    '@nuxt/scripts',
    '@nuxtjs/i18n',
    '@pinia/nuxt',
    '@peterbud/nuxt-query',
    '@vueuse/nuxt',
  ],

  $development: {
    //
  },

  $env: {
    staging: {
      //
    },
  },

  $production: {
    routeRules: {
      '/**': { isr: true },
    },
  },

  ssr: true,

  components: true,

  devtools: {
    enabled: true,
    timeline: {
      enabled: true,
    },
  },

  app: {
    head: {
      /* NOTE: 외부 static css 등을 추가하고 싶을 때,
      link: [{ rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css' }]
      */
    },
  },

  css: ['~/assets/css/main.css'],

  ui: {
    // prefix: 'U',
    // colorMode: false,
    // fonts: false,
    theme: {
      colors: [
        'primary',
        'secondary',
        'tertiary',
        'info',
        'success',
        'warning',
        'error',
      ],
    },
  },

  alias: {
    images: fileURLToPath(new URL('./assets/images', import.meta.url)),
    style: fileURLToPath(new URL('./assets/style', import.meta.url)),
    data: fileURLToPath(new URL('./assets/other/data', import.meta.url)),
    store: fileURLToPath(new URL('./stores', import.meta.url)),
    utils: fileURLToPath(new URL('./utils', import.meta.url)),
  },

  sourcemap: true,
  /* sourcemap: {
    'server': true,
    'client': false
  }, */

  devServer: {
    host: 'localhost',
    port: 4200,
  },

  future: {
    compatibilityVersion: 4,
  },

  // when enabling ssr options, you need to disable inlineStyles and maybe devLogs
  features: {
    devLogs: false,
    inlineStyles: false,
  },

  experimental: {
    debugModuleMutation: false,
  },

  compatibilityDate: '2025-07-16',

  nitro: {
    minify: false,
    inlineDynamicImports: true,
  },

  vite: {
    plugins: [
      oxlint(),
      eslint(),
      stylelint(),
      /* federation({
        name: 'nuxhost',
        remotes: {
          '@namespace/viteViteRemote': 'viteRemote@http://localhost:3000/_nuxt/mf-manifest.json',
        },
        filename: 'remoteEntry.js',
        shared: {
          // vue: {},
        },
        runtimePlugins: ['/federation/plugins/runtime'],
        // exposes: {
        //   "./App": "./App.vue"
        // }
        // manifest: {
        //   fileName: "_nuxt/mf-manifest.json",
        // }
      }),
      topLevelAwait({
      // The export name of top-level await promise for each chunk module
        promiseExportName: '__tla',
        // The function to generate import names of top-level await promise in each chunk module
        promiseImportName: (i) => `__tla_${i}`,
      }), */
    ],
    css: {
      devSourcemap: true,
      /* preprocessorOptions: {
        scss: {
        },
      }, */
    },
    vue: {
      customElement: true,
    },
    vueJsx: {
      mergeProps: true,
    },
  },

  debug: true,

  eslint: {
    config: {
      stylistic: {
        quotes: 'single', // Enforce single quotes
        // You can also add other stylistic rules here, e.g.,
        semi: true,
        indent: 2,
      },
    },
  },

  i18n: {
    langDir: 'locales',
    locales: [
      { code: 'ko', language: 'ko-KR', file: 'ko.json', name: '한국어' },
      { code: 'en', language: 'en-US', file: 'en.json', name: 'English' },
      { code: 'fr', language: 'fr-FR', file: 'fr.json', name: 'French' },
    ],
    defaultLocale: 'ko',
    experimental: {
      localeDetector: 'localeDetector.ts',
      preload: true,
    },
  },

  nuxtQuery: {
    autoImports: [
      'useQuery',
      'useInfiniteQuery',
      'useMutation',
      'useQueryClient',
      'useIsFetching',
      'useIsMutating',
    ],
    devtools: true,
    /**
     * These are the same options as the QueryClient
     * from @tanstack/vue-query, which will be passed
     * to the QueryClient constructor
     * More details: https://tanstack.com/query/v5/docs/reference/QueryClient
     */
    queryClientOptions: {
      defaultOptions: {
        queries: {
          // for example disable refetching on window focus
          refetchOnWindowFocus: false,

          // or change the default refetch interval
          refetchInterval: 5000,
        },
      },
    },
  },

  pinia: {
    storesDirs: [
      './stores/**',
    ],
  },
});